# nova_tools.py
from duckduckgo_search import DDGS
import requests
import json
from utils.helper_functions import truncate_string, format_source
from config import nova_config as conf
from config.api_keys import OPENROUTER_API_KEY

# Import all Nova's new capabilities
from core.nova_weather import get_weather, get_forecast
from core.nova_datetime import get_current_time, get_date_info, time_until, get_timezone_info
from core.nova_reminders import add_reminder, list_reminders, remove_reminder, add_plan, list_plans
from core.nova_windows import (set_volume, get_volume, mute_volume, unmute_volume,
                              open_application, close_application, list_running_apps,
                              take_screenshot, lock_computer, shutdown_computer, restart_computer)
from core.nova_knowledge import add_knowledge, search_knowledge, scrape_and_store, get_recent_knowledge

def perform_web_search(query, max_results=conf.MAX_SEARCH_RESULTS):
    """Perform web search and return cleaned results"""
    try:
        with DDGS() as ddgs:
            results = []
            dedupe = set()
            
            for result in ddgs.text(query, max_results=max_results*3):
                # Deduplicate by domain
                domain = result.get('hostname', result.get('href','').split('//')[-1].split('/')[0])
                if domain in dedupe:
                    continue
                dedupe.add(domain)
                
                results.append({
                    'title': result.get('title', 'No title'),
                    'snippet': result.get('body', 'No description'),
                    'url': result.get('href', ''),
                    'source': format_source(domain)
                })
                if len(results) >= max_results:
                    break
                    
            return results
    except Exception:
        return []

def create_conversational_summary(results, query):
    """Transform search results into Nova's natural narrative style"""
    if not results:
        return "I couldn't find fresh information on that topic just now. Perhaps try a different phrasing?"
    
    sources = {res['source'] for res in results}
    
    summary_lines = [
        f"🌐 I dug up some fresh info about '{query}' from around the web:",
        ""
    ]
    
    for i, res in enumerate(results, 1):
        summary_lines.append(f"🔹 **{res['source']}**: "
                            f"{truncate_string(res['snippet'], conf.CONVO_TRUNCATE_LENGTH)}")
    
    sources_str = ", ".join(sorted(sources))
    summary_lines.extend([
        "",
        f"📖 *Sources*: {sources_str}",
        "",
        "Would you like me to explore anything specific about what I found?"
    ])
    
    return "\n".join(summary_lines)

def process_tool_request(user_input):
    """Process user input and execute appropriate tools"""
    user_lower = user_input.lower()

    # Skip tool detection for simple greetings and casual conversation
    simple_greetings = ["hi", "hello", "hey", "how are you", "how you doing", "good morning",
                       "good afternoon", "good evening", "what's up", "sup", "yo", "how's it going"]

    # Check if this is just a simple greeting
    if any(greeting in user_lower for greeting in simple_greetings) and len(user_input.strip()) < 25:
        return None  # Let normal conversation handle it

    # Weather requests
    if any(phrase in user_lower for phrase in ["weather", "temperature", "forecast", "rain", "sunny", "cloudy"]):
        if "forecast" in user_lower or "tomorrow" in user_lower or "week" in user_lower:
            # Extract city if mentioned
            city = extract_city_from_input(user_input)
            return get_forecast(city)
        else:
            city = extract_city_from_input(user_input)
            return get_weather(city)

    # Time and date requests (be more specific to avoid false matches)
    elif any(phrase in user_lower for phrase in ["what time", "current time", "time is it", "clock"]):
        return get_current_time()
    elif any(phrase in user_lower for phrase in ["what date", "today's date", "date today", "calendar", "what day"]) and not any(phrase in user_lower for phrase in ["how", "doing", "are you"]):
        return get_date_info()
    elif "time until" in user_lower or "how long until" in user_lower:
        # Extract target time from input
        target = user_input.split("until")[-1].strip() if "until" in user_input else ""
        return time_until(target) if target else "Please specify what time you want to count down to!"

    # Reminder requests
    elif any(phrase in user_lower for phrase in ["remind me", "set reminder", "reminder"]):
        if "list" in user_lower or "show" in user_lower:
            return list_reminders()
        elif "remove" in user_lower or "delete" in user_lower:
            # Extract reminder ID
            words = user_input.split()
            for word in words:
                if len(word) >= 8 and word.isalnum():  # Likely a reminder ID
                    return remove_reminder(word)
            return "Please specify the reminder ID to remove."
        else:
            # Parse reminder from input
            return parse_and_add_reminder(user_input)

    # Plan requests
    elif any(phrase in user_lower for phrase in ["save plan", "add plan", "create plan", "plan"]):
        if "list" in user_lower or "show" in user_lower:
            return list_plans()
        else:
            return parse_and_add_plan(user_input)

    # Volume control
    elif any(phrase in user_lower for phrase in ["volume", "sound", "audio"]):
        if "mute" in user_lower:
            return mute_volume()
        elif "unmute" in user_lower:
            return unmute_volume()
        elif any(word.isdigit() for word in user_input.split()):
            # Extract volume level
            for word in user_input.split():
                if word.isdigit():
                    return set_volume(int(word))
        else:
            return get_volume()

    # Application control
    elif any(phrase in user_lower for phrase in ["open", "launch", "start"]) and not "search" in user_lower:
        app_name = extract_app_name(user_input, ["open", "launch", "start"])
        return open_application(app_name) if app_name else "Please specify which application to open."
    elif any(phrase in user_lower for phrase in ["close", "quit", "exit"]) and not user_lower in ["exit", "quit", "bye"]:
        app_name = extract_app_name(user_input, ["close", "quit", "exit"])
        return close_application(app_name) if app_name else "Please specify which application to close."
    elif "running apps" in user_lower or "list apps" in user_lower:
        return list_running_apps()

    # System control
    elif "screenshot" in user_lower or "screen shot" in user_lower:
        return take_screenshot()
    elif "lock computer" in user_lower or "lock screen" in user_lower:
        return lock_computer()
    elif "shutdown" in user_lower:
        return shutdown_computer()
    elif "restart" in user_lower:
        return restart_computer()

    # Knowledge base
    elif any(phrase in user_lower for phrase in ["remember this", "save this", "add to knowledge"]):
        content = user_input.replace("remember this", "").replace("save this", "").replace("add to knowledge", "").strip()
        return add_knowledge("User Note", content)
    elif "search knowledge" in user_lower or "what do you know about" in user_lower:
        query = user_input.replace("search knowledge", "").replace("what do you know about", "").strip()
        return search_knowledge(query) if query else "What would you like me to search for?"
    elif "recent knowledge" in user_lower:
        return get_recent_knowledge()

    # Web search requests
    elif any(phrase in user_lower for phrase in ["search for", "look up", "find information", "web search", "search the web"]):
        query = user_input
        # Clean up the query
        for phrase in ["search for", "look up", "find information about", "web search", "search the web for"]:
            query = query.lower().replace(phrase, "").strip()

        if query:
            search_results = perform_web_search(query)
            return create_conversational_summary(search_results, query)
        else:
            return "What would you like me to search for? 🔍"

    # News requests
    elif any(phrase in user_lower for phrase in ["news", "latest news", "current events", "headlines", "breaking news"]):
        # Extract topic if mentioned
        news_query = user_input
        for phrase in ["news about", "latest news on", "news on", "headlines about"]:
            if phrase in user_lower:
                news_query = user_input.lower().split(phrase)[-1].strip()
                break
        else:
            # If no specific topic, get general news
            if any(word in user_lower for word in ["latest", "current", "today", "breaking"]):
                news_query = "latest news today"
            else:
                news_query = user_input

        search_results = perform_web_search(news_query)
        return create_conversational_summary(search_results, news_query)

    # If no tool matches, return None to use normal conversation
    return None

def extract_city_from_input(user_input):
    """Extract city name from weather request"""
    # Simple city extraction - can be enhanced
    words = user_input.split()
    prepositions = ["in", "for", "at"]

    for i, word in enumerate(words):
        if word.lower() in prepositions and i + 1 < len(words):
            return " ".join(words[i+1:]).strip("?.,!")

    return None

def extract_app_name(user_input, action_words):
    """Extract application name from open/close request"""
    words = user_input.split()

    for action in action_words:
        if action in user_input.lower():
            parts = user_input.lower().split(action)
            if len(parts) > 1:
                app_name = parts[1].strip()
                # Remove common words
                app_name = app_name.replace("the", "").replace("application", "").replace("app", "").strip()
                return app_name

    return None

def parse_and_add_reminder(user_input):
    """Parse reminder from natural language input"""
    # Simple parsing - can be enhanced with NLP
    try:
        # Look for time patterns
        import re
        from dateutil import parser

        # Common patterns
        time_patterns = [
            r'at (\d{1,2}:\d{2})',
            r'at (\d{1,2} ?[ap]m)',
            r'in (\d+) (minute|hour|day)s?',
            r'(tomorrow|today) at (\d{1,2}:\d{2})',
            r'(tomorrow|today) at (\d{1,2} ?[ap]m)'
        ]

        reminder_text = user_input
        when = None

        for pattern in time_patterns:
            match = re.search(pattern, user_input.lower())
            if match:
                time_str = match.group(0)
                reminder_text = user_input.replace(match.group(0), "").replace("remind me", "").strip()

                try:
                    when = parser.parse(time_str)
                    break
                except:
                    continue

        if when and reminder_text:
            return add_reminder(reminder_text, when)
        else:
            return "I couldn't understand the time format. Try something like 'remind me to call John at 3pm' or 'remind me to take medicine in 2 hours'."

    except Exception as e:
        return f"Sorry, I had trouble parsing that reminder: {str(e)}"

def parse_and_add_plan(user_input):
    """Parse plan from natural language input"""
    # Extract plan name and details
    plan_text = user_input.replace("save plan", "").replace("add plan", "").replace("create plan", "").strip()

    if not plan_text:
        return "Please provide plan details. For example: 'Save plan: Weekend trip to Paris - book flights, hotel, and activities'"

    # Split on common separators
    if ":" in plan_text:
        parts = plan_text.split(":", 1)
        plan_name = parts[0].strip()
        plan_details = parts[1].strip() if len(parts) > 1 else ""
    elif "-" in plan_text:
        parts = plan_text.split("-", 1)
        plan_name = parts[0].strip()
        plan_details = parts[1].strip() if len(parts) > 1 else ""
    else:
        plan_name = plan_text[:50] + "..." if len(plan_text) > 50 else plan_text
        plan_details = plan_text

    return add_plan(plan_name, plan_details)

# Chat Engine Integration
def get_chat_response(messages):
    """Get response from OpenRouter API"""
    from core.nova_logger import log_api_call, log_error, log_debug

    headers = {
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        "HTTP-Referer": conf.WEBSITE_URL,
        "X-Title": conf.APP_NAME,
        "Content-Type": "application/json"
    }

    payload = {
        "model": "deepseek/deepseek-r1",  # Correct model name
        "messages": messages,
        "temperature": 0.7,
        "max_tokens": 1000
    }

    log_debug("API", f"Making API call with {len(messages)} messages")

    try:
        response = requests.post(conf.OPENROUTER_URL, headers=headers, json=payload, timeout=30)

        log_debug("API", f"Response status: {response.status_code}")

        if response.status_code == 200:
            try:
                # Check if response has content before parsing
                if not response.text or response.text.strip() == "":
                    log_error("API_EMPTY", "Received empty response from API")
                    return "I received an empty response. Could you try asking again? 🤔"

                response_data = response.json()
                log_debug("API", f"Response data keys: {list(response_data.keys())}")

                # Check if response has the expected structure
                if 'choices' in response_data and len(response_data['choices']) > 0:
                    choice = response_data['choices'][0]
                    if 'message' in choice and 'content' in choice['message']:
                        content = choice['message']['content']
                        if content and content.strip():
                            log_api_call("deepseek/deepseek-r1", 200, content)
                            return content
                        else:
                            log_error("API_EMPTY_CONTENT", "Received empty content in response")
                            return "I'm having trouble forming a response. Could you try rephrasing? 🤔"
                    else:
                        log_error("API_STRUCTURE", f"Missing message/content in choice: {choice}")
                        return "I'm having trouble connecting to my brain right now. Could you try asking again? 🤔"
                else:
                    log_error("API_STRUCTURE", f"Unexpected response structure: {response_data}")
                    return "I'm having trouble connecting to my brain right now. Could you try asking again? 🤔"

            except json.JSONDecodeError as e:
                log_error("API_JSON", f"JSON decode error: {e}", response.text[:200])
                return "🔧 I received a garbled response. Could you rephrase your question?"
        else:
            log_error("API_STATUS", f"Status {response.status_code}: {response.text}")
            return f"🔇 I'm experiencing some technical difficulties (Error {response.status_code}). Let me try to help you anyway!"

    except requests.exceptions.Timeout:
        log_error("API_TIMEOUT", "Request timed out after 30 seconds")
        return "⏱️ My response is taking longer than usual. Could you try asking again?"
    except requests.exceptions.ConnectionError as e:
        log_error("API_CONNECTION", f"Connection error: {e}")
        return "🌐 I'm having trouble connecting to the internet. Please check your connection and try again."
    except Exception as e:
        log_error("API_UNEXPECTED", f"Unexpected error: {e}")
        return "😅 Something unexpected happened. Let me try to help you in a different way!"
