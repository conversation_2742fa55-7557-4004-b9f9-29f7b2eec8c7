# test_api.py
"""
Test script to check DeepSeek R1 API connectivity
"""

import requests
import json
from config.api_keys import OPENROUTER_API_KEY
from config.nova_config import OPENROUTER_URL, APP_NAME, WEBSITE_URL

def test_api_connection():
    """Test the API connection and model availability"""
    print("🧪 Testing DeepSeek R1 API Connection...")
    print(f"API URL: {OPENROUTER_URL}")
    print(f"API Key: {OPENROUTER_API_KEY[:20]}...")
    
    headers = {
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        "HTTP-Referer": WEBSITE_URL,
        "X-Title": APP_NAME,
        "Content-Type": "application/json"
    }

    # Test with a simple message
    payload = {
        "model": "deepseek/deepseek-r1",
        "messages": [
            {"role": "system", "content": "You are a helpful AI assistant."},
            {"role": "user", "content": "Hello, can you respond with just 'API test successful'?"}
        ],
        "temperature": 0.7,
        "max_tokens": 50
    }

    try:
        print("\n📡 Making API request...")
        response = requests.post(OPENROUTER_URL, headers=headers, json=payload, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Text Length: {len(response.text)}")
        print(f"Response Text (first 500 chars): {response.text[:500]}")
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                print(f"\n✅ JSON parsed successfully!")
                print(f"Response keys: {list(response_data.keys())}")
                
                if 'choices' in response_data and len(response_data['choices']) > 0:
                    content = response_data['choices'][0]['message']['content']
                    print(f"✅ DeepSeek R1 Response: {content}")
                    return True
                else:
                    print(f"❌ Unexpected response structure: {response_data}")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                return False
        else:
            print(f"❌ API request failed with status {response.status_code}")
            print(f"Error response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"❌ Connection error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_alternative_models():
    """Test alternative models if DeepSeek R1 fails"""
    print("\n🧪 Testing alternative models...")
    
    alternative_models = [
        "deepseek/deepseek-chat",
        "openai/gpt-3.5-turbo",
        "anthropic/claude-3-haiku",
        "meta-llama/llama-3.1-8b-instruct"
    ]
    
    headers = {
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        "HTTP-Referer": WEBSITE_URL,
        "X-Title": APP_NAME,
        "Content-Type": "application/json"
    }
    
    for model in alternative_models:
        print(f"\n🔍 Testing {model}...")
        
        payload = {
            "model": model,
            "messages": [
                {"role": "user", "content": "Hello"}
            ],
            "temperature": 0.7,
            "max_tokens": 20
        }
        
        try:
            response = requests.post(OPENROUTER_URL, headers=headers, json=payload, timeout=15)
            
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    if 'choices' in response_data and len(response_data['choices']) > 0:
                        content = response_data['choices'][0]['message']['content']
                        print(f"✅ {model} works: {content[:50]}...")
                    else:
                        print(f"⚠️ {model} returned unexpected structure")
                except json.JSONDecodeError:
                    print(f"❌ {model} returned invalid JSON")
            else:
                print(f"❌ {model} failed with status {response.status_code}")
                
        except Exception as e:
            print(f"❌ {model} error: {e}")

def check_openrouter_models():
    """Check available models on OpenRouter"""
    print("\n🧪 Checking available models on OpenRouter...")
    
    try:
        # OpenRouter models endpoint
        models_url = "https://openrouter.ai/api/v1/models"
        headers = {
            "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        }
        
        response = requests.get(models_url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            models_data = response.json()
            if 'data' in models_data:
                deepseek_models = [model for model in models_data['data'] if 'deepseek' in model.get('id', '').lower()]
                print(f"✅ Found {len(deepseek_models)} DeepSeek models:")
                for model in deepseek_models[:5]:  # Show first 5
                    print(f"  • {model.get('id', 'Unknown')}")
                
                # Check if deepseek-r1 is available
                r1_available = any('deepseek-r1' in model.get('id', '') for model in deepseek_models)
                if r1_available:
                    print("✅ DeepSeek R1 is available!")
                else:
                    print("❌ DeepSeek R1 not found in available models")
                    
            else:
                print("❌ Unexpected models response structure")
        else:
            print(f"❌ Failed to fetch models: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error checking models: {e}")

if __name__ == "__main__":
    print("🚀 DeepSeek R1 API Diagnostic Test")
    print("=" * 50)
    
    # Test main API
    success = test_api_connection()
    
    if not success:
        # Test alternatives
        test_alternative_models()
        
        # Check available models
        check_openrouter_models()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 DeepSeek R1 is working correctly!")
    else:
        print("⚠️ DeepSeek R1 has issues. Check the output above for details.")
