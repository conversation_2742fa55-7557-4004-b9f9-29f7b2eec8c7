# nova_knowledge.py
import sqlite3
import json
import os
from datetime import datetime
import hashlib
from urllib.parse import urlparse
import requests
from bs4 import BeautifulSoup
from config import nova_config as conf

class KnowledgeBase:
    def __init__(self):
        self.db_path = conf.KNOWLEDGE_BASE_FILE
        self.init_database()
    
    def init_database(self):
        """Initialize the knowledge base database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create tables
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS knowledge_entries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                topic TEXT NOT NULL,
                content TEXT NOT NULL,
                source TEXT,
                source_type TEXT,
                url TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                hash TEXT UNIQUE,
                tags TEXT,
                importance INTEGER DEFAULT 1
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS web_cache (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                url TEXT UNIQUE NOT NULL,
                title TEXT,
                content TEXT,
                scraped_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_accessed DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS conversation_context (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                context_key TEXT NOT NULL,
                context_value TEXT NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def add_knowledge(self, topic, content, source="user", source_type="manual", url=None, tags=None, importance=1):
        """Add knowledge to the database"""
        try:
            # Create hash for deduplication
            content_hash = hashlib.md5(f"{topic}:{content}".encode()).hexdigest()
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check if already exists
            cursor.execute("SELECT id FROM knowledge_entries WHERE hash = ?", (content_hash,))
            if cursor.fetchone():
                conn.close()
                return f"📚 **Knowledge Already Exists**\n\nThis information is already in my knowledge base! 🧠"
            
            # Insert new knowledge
            cursor.execute('''
                INSERT INTO knowledge_entries 
                (topic, content, source, source_type, url, hash, tags, importance)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (topic, content, source, source_type, url, content_hash, 
                  json.dumps(tags) if tags else None, importance))
            
            conn.commit()
            conn.close()
            
            return f"✅ **Knowledge Added!**\n\n📝 **Topic**: {topic}\n📚 **Source**: {source}\n\nI've learned something new! 🧠✨"
            
        except Exception as e:
            return f"❌ Sorry, couldn't save that knowledge: {str(e)}"
    
    def search_knowledge(self, query, limit=5):
        """Search knowledge base for relevant information"""
        try:
            # Check if database exists and has tables
            if not os.path.exists(self.db_path):
                return f"🔍 **No Knowledge Found**\n\nI don't have information about '{query}' in my knowledge base yet. Would you like me to search the web for it? 🌐"

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Check if table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='knowledge_entries'")
            if not cursor.fetchone():
                conn.close()
                return f"🔍 **No Knowledge Found**\n\nI don't have information about '{query}' in my knowledge base yet. Would you like me to search the web for it? 🌐"

            # Search in topic and content
            search_query = f"%{query.lower()}%"
            cursor.execute('''
                SELECT topic, content, source, url, timestamp, importance
                FROM knowledge_entries
                WHERE LOWER(topic) LIKE ? OR LOWER(content) LIKE ?
                ORDER BY importance DESC, timestamp DESC
                LIMIT ?
            ''', (search_query, search_query, limit))

            results = cursor.fetchall()
            conn.close()

            if not results:
                return f"🔍 **No Knowledge Found**\n\nI don't have information about '{query}' in my knowledge base yet. Would you like me to search the web for it? 🌐"

            response = f"🧠 **Knowledge Base Results for '{query}'**\n\n"

            for i, (topic, content, source, url, timestamp, importance) in enumerate(results, 1):
                # Truncate content if too long
                display_content = content[:200] + "..." if len(content) > 200 else content

                response += f"**{i}. {topic}**\n"
                response += f"📄 {display_content}\n"
                response += f"📚 Source: {source}"

                if url:
                    response += f" ({url})"

                response += f"\n⏰ Added: {timestamp[:10]}\n\n"

            response += "Need more details about any of these? Just ask! 💡"
            return response

        except Exception as e:
            # Return a safe fallback instead of error
            return f"🔍 **No Knowledge Found**\n\nI don't have information about '{query}' in my knowledge base yet. Would you like me to search the web for it? 🌐"
    
    def scrape_and_store(self, url, topic=None):
        """Scrape web content and store in knowledge base"""
        try:
            # Check if already cached
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT title, content FROM web_cache WHERE url = ?", (url,))
            cached = cursor.fetchone()
            
            if cached:
                title, content = cached
                # Update last accessed
                cursor.execute("UPDATE web_cache SET last_accessed = CURRENT_TIMESTAMP WHERE url = ?", (url,))
                conn.commit()
            else:
                # Scrape new content
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                }
                
                response = requests.get(url, headers=headers, timeout=10)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Extract title
                title = soup.find('title')
                title = title.get_text().strip() if title else urlparse(url).netloc
                
                # Extract main content
                # Remove script and style elements
                for script in soup(["script", "style"]):
                    script.decompose()
                
                # Get text content
                content = soup.get_text()
                
                # Clean up content
                lines = (line.strip() for line in content.splitlines())
                chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                content = ' '.join(chunk for chunk in chunks if chunk)
                
                # Limit content length
                if len(content) > 5000:
                    content = content[:5000] + "..."
                
                # Cache the content
                cursor.execute('''
                    INSERT OR REPLACE INTO web_cache (url, title, content)
                    VALUES (?, ?, ?)
                ''', (url, title, content))
                
                conn.commit()
            
            # Add to knowledge base
            if not topic:
                topic = title
            
            domain = urlparse(url).netloc
            knowledge_result = self.add_knowledge(
                topic=topic,
                content=content,
                source=domain,
                source_type="web_scrape",
                url=url,
                importance=2
            )
            
            conn.close()
            
            return f"🌐 **Web Content Scraped & Stored**\n\n📰 **Title**: {title}\n🔗 **URL**: {url}\n📚 **Added to Knowledge Base**: {topic}\n\n{knowledge_result}"
            
        except Exception as e:
            return f"🌐 Sorry, couldn't scrape that webpage: {str(e)}"
    
    def get_recent_knowledge(self, limit=10):
        """Get recently added knowledge"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT topic, content, source, timestamp
                FROM knowledge_entries 
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (limit,))
            
            results = cursor.fetchall()
            conn.close()
            
            if not results:
                return "📚 **No Knowledge Entries Yet**\n\nYour knowledge base is empty. Let's start learning together! 🌱"
            
            response = f"📚 **Recent Knowledge ({limit} entries)**\n\n"
            
            for i, (topic, content, source, timestamp) in enumerate(results, 1):
                display_content = content[:100] + "..." if len(content) > 100 else content
                response += f"**{i}. {topic}**\n"
                response += f"📄 {display_content}\n"
                response += f"📚 {source} • {timestamp[:10]}\n\n"
            
            return response
            
        except Exception as e:
            return f"📚 Sorry, couldn't get recent knowledge: {str(e)}"
    
    def save_context(self, key, value):
        """Save conversation context"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO conversation_context (context_key, context_value)
                VALUES (?, ?)
            ''', (key, value))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error saving context: {e}")
    
    def get_context(self, key, limit=5):
        """Get conversation context"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT context_value, timestamp
                FROM conversation_context 
                WHERE context_key = ?
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (key, limit))
            
            results = cursor.fetchall()
            conn.close()
            
            return [result[0] for result in results]
            
        except Exception as e:
            print(f"Error getting context: {e}")
            return []
    
    def cleanup_old_entries(self):
        """Clean up old entries to maintain performance"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Keep only the most recent entries
            cursor.execute('''
                DELETE FROM knowledge_entries 
                WHERE id NOT IN (
                    SELECT id FROM knowledge_entries 
                    ORDER BY importance DESC, timestamp DESC 
                    LIMIT ?
                )
            ''', (conf.MAX_KNOWLEDGE_ENTRIES,))
            
            # Clean old web cache (older than 30 days)
            cursor.execute('''
                DELETE FROM web_cache 
                WHERE last_accessed < datetime('now', '-30 days')
            ''')
            
            # Clean old context (older than 7 days)
            cursor.execute('''
                DELETE FROM conversation_context 
                WHERE timestamp < datetime('now', '-7 days')
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error cleaning up knowledge base: {e}")

# Global knowledge base instance
knowledge_base = KnowledgeBase()

# Main functions for Nova to use
def add_knowledge(topic, content, source="user", url=None, tags=None):
    """Add knowledge - main function for Nova to use"""
    return knowledge_base.add_knowledge(topic, content, source, url=url, tags=tags)

def search_knowledge(query):
    """Search knowledge - main function for Nova to use"""
    return knowledge_base.search_knowledge(query)

def scrape_and_store(url, topic=None):
    """Scrape and store - main function for Nova to use"""
    return knowledge_base.scrape_and_store(url, topic)

def get_recent_knowledge():
    """Get recent knowledge - main function for Nova to use"""
    return knowledge_base.get_recent_knowledge()

def save_context(key, value):
    """Save context - main function for Nova to use"""
    return knowledge_base.save_context(key, value)

def get_context(key):
    """Get context - main function for Nova to use"""
    return knowledge_base.get_context(key)
