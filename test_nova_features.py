# test_nova_features.py
"""
Comprehensive test script for Nova AI Agent
Tests all major features to ensure they work correctly
"""

import sys
import os
import traceback
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test if all modules can be imported"""
    print("🧪 Testing imports...")
    try:
        from core import nova_tools, nova_memory, nova_voice, nova_chat
        from core import nova_weather, nova_datetime, nova_reminders
        from core import nova_windows, nova_knowledge, nova_logger
        from config import nova_config, api_keys
        print("✅ All core modules imported successfully")
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        traceback.print_exc()
        return False

def test_tool_detection():
    """Test tool detection system"""
    print("\n🧪 Testing tool detection...")
    try:
        from core.nova_tools import process_tool_request
        
        test_cases = [
            ("Hi Nova, how are you?", None),  # Should not trigger tools
            ("What's the weather?", "weather"),  # Should trigger weather
            ("What time is it?", "time"),  # Should trigger time
            ("Set volume to 50", "volume"),  # Should trigger volume
            ("Search for AI news", "search"),  # Should trigger search
        ]
        
        for input_text, expected_type in test_cases:
            result = process_tool_request(input_text)
            if expected_type is None:
                if result is None:
                    print(f"✅ '{input_text}' correctly ignored")
                else:
                    print(f"⚠️ '{input_text}' unexpectedly triggered: {result[:50]}...")
            else:
                if result is not None:
                    print(f"✅ '{input_text}' correctly triggered {expected_type}")
                else:
                    print(f"❌ '{input_text}' failed to trigger {expected_type}")
        
        return True
    except Exception as e:
        print(f"❌ Tool detection test error: {e}")
        traceback.print_exc()
        return False

def test_memory_system():
    """Test memory system"""
    print("\n🧪 Testing memory system...")
    try:
        from core.nova_memory import load_memory, save_memory, clear_memory
        
        # Test loading memory
        memory = load_memory()
        print(f"✅ Memory loaded: {len(memory)} entries")
        
        # Test saving memory
        save_memory("Test input", "Test response")
        print("✅ Memory saved successfully")
        
        # Test loading after save
        updated_memory = load_memory()
        print(f"✅ Updated memory loaded: {len(updated_memory)} entries")
        
        return True
    except Exception as e:
        print(f"❌ Memory system test error: {e}")
        traceback.print_exc()
        return False

def test_knowledge_base():
    """Test knowledge base system"""
    print("\n🧪 Testing knowledge base...")
    try:
        from core.nova_knowledge import add_knowledge, search_knowledge
        
        # Test adding knowledge
        result = add_knowledge("Test Topic", "This is test content", "test")
        print(f"✅ Knowledge added: {result[:50]}...")
        
        # Test searching knowledge
        search_result = search_knowledge("test")
        print(f"✅ Knowledge search: {search_result[:50]}...")
        
        return True
    except Exception as e:
        print(f"❌ Knowledge base test error: {e}")
        traceback.print_exc()
        return False

def test_datetime_functions():
    """Test date/time functions"""
    print("\n🧪 Testing date/time functions...")
    try:
        from core.nova_datetime import get_current_time, get_date_info
        
        # Test current time
        time_result = get_current_time()
        print(f"✅ Current time: {time_result}")
        
        # Test date info
        date_result = get_date_info()
        print(f"✅ Date info: {date_result}")
        
        return True
    except Exception as e:
        print(f"❌ Date/time test error: {e}")
        traceback.print_exc()
        return False

def test_weather_system():
    """Test weather system"""
    print("\n🧪 Testing weather system...")
    try:
        from core.nova_weather import get_weather
        
        # Test weather (will show API key message if not configured)
        weather_result = get_weather("London")
        print(f"✅ Weather result: {weather_result[:100]}...")
        
        return True
    except Exception as e:
        print(f"❌ Weather test error: {e}")
        traceback.print_exc()
        return False

def test_windows_control():
    """Test Windows control functions"""
    print("\n🧪 Testing Windows control...")
    try:
        from core.nova_windows import get_volume, list_running_apps
        
        # Test volume control
        volume_result = get_volume()
        print(f"✅ Volume control: {volume_result}")
        
        # Test listing apps
        apps_result = list_running_apps()
        print(f"✅ Running apps: {apps_result[:100]}...")
        
        return True
    except Exception as e:
        print(f"❌ Windows control test error: {e}")
        traceback.print_exc()
        return False

def test_web_search():
    """Test web search functionality"""
    print("\n🧪 Testing web search...")
    try:
        from core.nova_tools import perform_web_search, create_conversational_summary
        
        # Test web search
        search_results = perform_web_search("Python programming", max_results=2)
        print(f"✅ Web search returned {len(search_results)} results")
        
        # Test summary creation
        if search_results:
            summary = create_conversational_summary(search_results, "Python programming")
            print(f"✅ Summary created: {summary[:100]}...")
        
        return True
    except Exception as e:
        print(f"❌ Web search test error: {e}")
        traceback.print_exc()
        return False

def test_voice_system():
    """Test voice system"""
    print("\n🧪 Testing voice system...")
    try:
        from core.nova_voice import NovaVoice, detect_speech_tone, adjust_voice_for_tone
        
        # Test voice initialization
        voice = NovaVoice()
        print("✅ Voice system initialized")
        
        # Test tone detection
        tone = detect_speech_tone("This is exciting news!")
        print(f"✅ Tone detection: {tone}")
        
        # Test voice adjustment (won't actually speak in test)
        adjust_voice_for_tone(voice, tone)
        print("✅ Voice adjustment completed")
        
        return True
    except Exception as e:
        print(f"❌ Voice system test error: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Nova AI Agent Feature Tests")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_tool_detection,
        test_memory_system,
        test_knowledge_base,
        test_datetime_functions,
        test_weather_system,
        test_windows_control,
        test_web_search,
        test_voice_system,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"🧪 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Nova is ready to go!")
    else:
        print("⚠️ Some tests failed. Check the errors above.")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
