# simple_api_test.py
"""
Simple API test to diagnose DeepSeek R1 issues
"""

import requests
import json

# API configuration
API_KEY = "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"
API_URL = "https://openrouter.ai/api/v1/chat/completions"

def test_simple_request():
    """Test a simple API request"""
    print("🧪 Testing simple API request...")
    
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    # Try the free version first
    payload = {
        "model": "deepseek/deepseek-r1:free",
        "messages": [
            {"role": "user", "content": "Hello"}
        ],
        "max_tokens": 50
    }
    
    try:
        print("📡 Making request to OpenRouter...")
        response = requests.post(API_URL, headers=headers, json=payload, timeout=10)
        
        print(f"Status: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print(f"Content length: {len(response.text)}")
        print(f"Raw response: {response.text}")
        
        if response.status_code == 200:
            if response.text.strip():
                try:
                    data = response.json()
                    print("✅ JSON parsed successfully!")
                    print(f"Keys: {list(data.keys())}")
                    if 'choices' in data:
                        print(f"✅ Response: {data['choices'][0]['message']['content']}")
                    return True
                except json.JSONDecodeError as e:
                    print(f"❌ JSON error: {e}")
                    return False
            else:
                print("❌ Empty response body")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Request error: {e}")
        return False

def test_paid_version():
    """Test the paid version"""
    print("\n🧪 Testing paid version...")
    
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "deepseek/deepseek-r1",
        "messages": [
            {"role": "user", "content": "Hello"}
        ],
        "max_tokens": 50
    }
    
    try:
        response = requests.post(API_URL, headers=headers, json=payload, timeout=10)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text[:200]}...")
        
        if response.status_code == 200 and response.text.strip():
            try:
                data = response.json()
                print("✅ Paid version works!")
                return True
            except:
                print("❌ Paid version JSON error")
                return False
        else:
            print("❌ Paid version failed")
            return False
            
    except Exception as e:
        print(f"❌ Paid version error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Simple DeepSeek R1 API Test")
    print("=" * 40)
    
    # Test free version
    free_works = test_simple_request()
    
    # Test paid version
    paid_works = test_paid_version()
    
    print("\n" + "=" * 40)
    print(f"Free version: {'✅' if free_works else '❌'}")
    print(f"Paid version: {'✅' if paid_works else '❌'}")
    
    if not free_works and not paid_works:
        print("\n💡 Possible issues:")
        print("1. API key might be invalid")
        print("2. Network connectivity issues")
        print("3. OpenRouter service issues")
        print("4. Model temporarily unavailable")
